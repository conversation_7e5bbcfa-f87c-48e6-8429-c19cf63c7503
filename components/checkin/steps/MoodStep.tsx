import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Platform } from 'react-native';
import { <PERSON>, Frown, <PERSON><PERSON>, <PERSON>, Grin, Star } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { CheckInStepProps } from '../types';

const MOOD_OPTIONS = [
  { value: 1, emoji: '😢', icon: Frown, color: '#EF4444', labelNL: 'Slecht', labelEN: 'Poor' },
  { value: 2, emoji: '😕', icon: Frown, color: '#F97316', labelNL: 'Matig', labelEN: 'Fair' },
  { value: 3, emoji: '😐', icon: Meh, color: '#EAB308', labelNL: 'Oké', labelEN: 'Okay' },
  { value: 4, emoji: '😊', icon: Smile, color: '#22C55E', labelNL: 'Goed', labelEN: 'Good' },
  { value: 5, emoji: '😄', icon: Grin, color: '#10B981', labelNL: 'Uitstekend', labelEN: 'Excellent' },
];

const CRAVING_OPTIONS = [
  { value: 1, labelNL: 'Geen', labelEN: 'None', color: '#10B981' },
  { value: 2, labelNL: 'Licht', labelEN: 'Light', color: '#22C55E' },
  { value: 3, labelNL: 'Matig', labelEN: 'Moderate', color: '#EAB308' },
  { value: 4, labelNL: 'Sterk', labelEN: 'Strong', color: '#F97316' },
  { value: 5, labelNL: 'Intens', labelEN: 'Intense', color: '#EF4444' },
];

export const MoodStep: React.FC<CheckInStepProps> = ({
  checkInData,
  updateCheckInData,
  colors,
  language,
}) => {
  const handleMoodSelect = (mood: number) => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    updateCheckInData({ mood });
  };

  const handleCravingSelect = (cravingIntensity: number) => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    updateCheckInData({ cravingIntensity });
  };

  const handleNotesChange = (moodNotes: string) => {
    updateCheckInData({ moodNotes });
  };

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Heart size={24} color={colors.primary} />
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Hoe voel je je vandaag?' : 'How are you feeling today?'}
          </Text>
        </View>
        
        <View style={styles.moodGrid}>
          {MOOD_OPTIONS.map((option) => {
            const isSelected = checkInData.mood === option.value;
            const IconComponent = option.icon;
            
            return (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.moodOption,
                  {
                    backgroundColor: isSelected ? option.color + '20' : colors.card,
                    borderColor: isSelected ? option.color : colors.border,
                  },
                ]}
                onPress={() => handleMoodSelect(option.value)}
              >
                <Text style={styles.moodEmoji}>{option.emoji}</Text>
                <IconComponent 
                  size={20} 
                  color={isSelected ? option.color : colors.muted} 
                />
                <Text style={[
                  styles.moodLabel,
                  { color: isSelected ? option.color : colors.text }
                ]}>
                  {language === 'nl' ? option.labelNL : option.labelEN}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Star size={24} color={colors.warning || colors.primary} />
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Verlangen naar gebruik?' : 'Craving intensity?'}
          </Text>
        </View>
        
        <View style={styles.cravingGrid}>
          {CRAVING_OPTIONS.map((option) => {
            const isSelected = checkInData.cravingIntensity === option.value;
            
            return (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.cravingOption,
                  {
                    backgroundColor: isSelected ? option.color + '20' : colors.card,
                    borderColor: isSelected ? option.color : colors.border,
                  },
                ]}
                onPress={() => handleCravingSelect(option.value)}
              >
                <View style={[
                  styles.cravingDot,
                  { backgroundColor: isSelected ? option.color : colors.muted }
                ]} />
                <Text style={[
                  styles.cravingLabel,
                  { color: isSelected ? option.color : colors.text }
                ]}>
                  {language === 'nl' ? option.labelNL : option.labelEN}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {language === 'nl' ? 'Aanvullende notities (optioneel)' : 'Additional notes (optional)'}
        </Text>
        
        <TextInput
          style={[
            styles.notesInput,
            {
              backgroundColor: colors.card,
              borderColor: colors.border,
              color: colors.text,
            },
          ]}
          placeholder={
            language === 'nl'
              ? 'Hoe voel je je vandaag? Wat houdt je bezig?'
              : 'How are you feeling today? What\'s on your mind?'
          }
          placeholderTextColor={colors.muted}
          value={checkInData.moodNotes}
          onChangeText={handleNotesChange}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 32,
  },
  section: {
    gap: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  moodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  moodOption: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    gap: 8,
  },
  moodEmoji: {
    fontSize: 24,
  },
  moodLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  cravingGrid: {
    gap: 8,
  },
  cravingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    gap: 12,
  },
  cravingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  cravingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  notesInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
  },
});
