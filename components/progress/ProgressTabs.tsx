import React from "react";
import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Platform,
} from "react-native";
import { useUserStore } from "@/store/user/user-store";
import { MoodForm } from "@/components/progress/MoodForm";

import LogHealthModal from "@/components/health/LogHealthModal";
import { MoodTab } from "@/components/progress/MoodTab";

import { MilestonesTab } from "@/components/progress/MilestonesTab";

import {
  PlusCircle,
  BarChart2,
  LineChart as LineChartIcon,
  Award,
  Activity,
  AlertTriangle,
  Calendar,
  CheckCircle,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import { Animated } from "react-native";
import { Colors, ViewMode, Language, DataPoint } from "./types";
import { ProgressInsights } from "./shared/ProgressInsights";
import { HealthProgressTab } from "./HealthProgressTab";
import { UsageTab } from "./UsageTab";
import { RelapseTab } from "./RelapseTab";
import { RelapseForm } from "./RelapseForm";
import { CheckInHistory } from "@/components/checkin/CheckInHistory";
import {
  getHealthMetricEmoji,
  getHealthMetricLabel,
  getHealthMetricColor
} from "./utils";

interface ProgressTabsProps {
  colors: Colors;
  language: Language;
}

export const ProgressTabs: React.FC<ProgressTabsProps> = ({
  colors,
  language,
}) => {
  const { profile } = useUserStore();
  const [activeTab, setActiveTab] = useState("checkins");
  const [viewMode, setViewMode] = useState<ViewMode>("chart");
  const [showMoodForm, setShowMoodForm] = useState(false);
  const [showHealthModal, setShowHealthModal] = useState(false);
  const [showRelapseForm, setShowRelapseForm] = useState(false);
  const [editingMoodId, setEditingMoodId] = useState<string | null>(null);
  const [fadeAnim] = useState(new Animated.Value(1));

  const handleTabChange = (tab: string) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setActiveTab(tab);
  };

  const handleAddEntry = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    if (activeTab === "mood") {
      setEditingMoodId(null);
      setShowMoodForm(true);
    }
  };

  const handleEditMood = (id: string) => {
    setEditingMoodId(id);
    setShowMoodForm(true);
  };

  const handleDeleteMood = (_id: string) => {
    // Handle mood deletion
  };

  const handleViewModeChange = (mode: ViewMode) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setViewMode(mode);
  };

  const handleViewMilestones = () => {
    setActiveTab("milestones");
  };

  const handleEditHealth = (id: string) => {
    // TODO: Implement health entry editing
    console.log("Edit health entry:", id);
  };

  const handleCalendarDayPress = (date: Date, entries: { id: string; date: Date; value: number; type: string; metric?: string }[]) => {
    // TODO: Implement calendar day press functionality
    console.log("Calendar day pressed:", date, entries);
  };

  const handleEditRelapse = (id: string) => {
    // TODO: Implement relapse entry editing
    console.log("Edit relapse entry:", id);
  };

  const handleDeleteRelapse = (id: string) => {
    // TODO: Implement relapse entry deletion
    console.log("Delete relapse entry:", id);
  };

  // Generate chart data
  const moodChartData: DataPoint[] = profile?.moodEntries?.map(entry => ({
    id: entry.id,
    date: entry.date,
    value: entry.mood,
    label: entry.mood.toString(),
  })) || [];

  const cravingChartData: DataPoint[] = profile?.moodEntries?.map(entry => ({
    id: entry.id,
    date: entry.date,
    value: entry.cravingIntensity || 0,
    label: (entry.cravingIntensity || 0).toString(),
  })) || [];

  // Generate health chart data - group by metric type for better visualization
  const healthChartData: DataPoint[] = profile?.healthMetrics?.map(metric => ({
    id: metric.id,
    date: metric.date,
    value: typeof metric.value === 'number' ? metric.value : parseFloat(metric.value.toString()) || 0,
    label: `${metric.value} ${metric.unit || ''}`,
    metric: metric.metric,
    notes: metric.notes,
  })) || [];

  // Group health data by metric type for better chart visualization
  const healthDataByMetric = healthChartData.reduce((acc, item) => {
    if (!acc[item.metric || 'unknown']) {
      acc[item.metric || 'unknown'] = [];
    }
    acc[item.metric || 'unknown'].push(item);
    return acc;
  }, {} as Record<string, DataPoint[]>);

  // Generate health calendar entries
  const healthCalendarEntries = profile?.healthMetrics?.map(metric => ({
    id: metric.id,
    date: new Date(metric.date),
    value: typeof metric.value === 'number' ? metric.value : parseFloat(metric.value.toString()) || 0,
    type: "health" as const,
    metric: metric.metric,
  })) || [];





  // Comprehensive tab configuration with restored features
  const tabs = [
    {
      id: "checkins",
      label: language === "nl" ? "Check-ins" : "Check-ins",
      icon: CheckCircle,
      color: colors.primary,
    },
    {
      id: "mood",
      label: language === "nl" ? "Stemming" : "Mood",
      icon: Activity,
      color: colors.primary,
    },
    {
      id: "health",
      label: language === "nl" ? "Gezondheid" : "Health",
      icon: BarChart2,
      color: colors.info,
    },
    {
      id: "usage",
      label: language === "nl" ? "Gebruik" : "Usage",
      icon: LineChartIcon,
      color: colors.warning,
    },
    {
      id: "relapse",
      label: language === "nl" ? "Terugval" : "Relapse",
      icon: AlertTriangle,
      color: colors.danger,
    },
    {
      id: "milestones",
      label: language === "nl" ? "Mijlpalen" : "Milestones",
      icon: Award,
      color: colors.success,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "checkins":
        return <CheckInHistory />;
      case "mood":
        return (
          <MoodTab
            profile={profile}
            colors={colors}
            fadeAnim={fadeAnim}
            viewMode={viewMode}
            setViewMode={handleViewModeChange}
            moodChartData={moodChartData}
            cravingChartData={cravingChartData}
            handleEditMood={handleEditMood}
            handleDeleteMood={handleDeleteMood}
          />
        );
      case "health":
        return (
          <HealthProgressTab
            healthData={healthChartData}
            healthDataByMetric={healthDataByMetric}
            healthCalendarEntries={healthCalendarEntries}
            handleEditHealth={handleEditHealth}
            handleCalendarDayPress={handleCalendarDayPress}
            viewMode={viewMode}
            screenWidth={350}
            colors={colors}
            language={language}
            getHealthMetricEmoji={getHealthMetricEmoji}
            getHealthMetricLabel={getHealthMetricLabel}
            getHealthMetricColor={getHealthMetricColor}
          />
        );
      case "usage":
        return (
          <UsageTab
            profile={profile}
            colors={colors}
            language={language}
          />
        );
      case "relapse":
        return (
          <RelapseTab
            profile={profile}
            colors={colors}
            fadeAnim={fadeAnim}
            viewMode={viewMode}
            setViewMode={handleViewModeChange}
            handleEditRelapse={handleEditRelapse}
            handleDeleteRelapse={handleDeleteRelapse}
          />
        );
      case "milestones":
        return (
          <MilestonesTab
            profile={profile}
            colors={colors}
            language={language}
            fadeAnim={fadeAnim}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.mainScrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header Section */}
        <View style={styles.headerSection}>
          <Text style={[styles.pageTitle, { color: colors.text }]}>
            {language === "nl" ? "Voortgang" : "Progress"}
          </Text>
          <Text style={[styles.pageSubtitle, { color: colors.textSecondary }]}>
            {language === "nl" ? "Track je reis naar herstel" : "Track your recovery journey"}
          </Text>
        </View>

        {/* Progress Insights - Restored Feature */}
        {profile && (
          <ProgressInsights
            profile={profile}
            colors={colors}
            language={language}
          />
        )}

        {/* Enhanced Tab Navigation - Horizontal Scroll */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={[styles.tabsScrollContainer, { backgroundColor: colors.card }]}
          contentContainerStyle={styles.tabsContainer}
        >
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            const IconComponent = tab.icon;
            return (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  isActive && [
                    styles.activeTab,
                    { backgroundColor: tab.color + "15", borderColor: tab.color + "30" },
                  ],
                ]}
                onPress={() => handleTabChange(tab.id)}
              >
                <IconComponent
                  size={18}
                  color={isActive ? tab.color : colors.textSecondary}
                  strokeWidth={isActive ? 2.5 : 2}
                />
                <Text
                  style={[
                    styles.tabText,
                    { color: isActive ? tab.color : colors.textSecondary },
                    isActive && styles.activeTabText,
                  ]}
                >
                  {tab.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>

        {/* View Mode Toggle - Enhanced for multiple tabs */}
        {(activeTab === "mood" || activeTab === "health" || activeTab === "relapse") && (
          <View style={styles.viewModeContainer}>
            <TouchableOpacity
              style={[
                styles.viewModeButton,
                { backgroundColor: colors.card, borderColor: colors.border },
                viewMode === "chart" && [
                  styles.activeViewModeButton,
                  { backgroundColor: colors.primary + "15", borderColor: colors.primary + "30" },
                ],
              ]}
              onPress={() => handleViewModeChange("chart")}
            >
              <LineChartIcon
                size={16}
                color={viewMode === "chart" ? colors.primary : colors.textSecondary}
                strokeWidth={2}
              />
              <Text style={[
                styles.viewModeText,
                { color: viewMode === "chart" ? colors.primary : colors.textSecondary }
              ]}>
                {language === "nl" ? "Grafiek" : "Chart"}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.viewModeButton,
                { backgroundColor: colors.card, borderColor: colors.border },
                viewMode === "list" && [
                  styles.activeViewModeButton,
                  { backgroundColor: colors.primary + "15", borderColor: colors.primary + "30" },
                ],
              ]}
              onPress={() => handleViewModeChange("list")}
            >
              <BarChart2
                size={16}
                color={viewMode === "list" ? colors.primary : colors.textSecondary}
                strokeWidth={2}
              />
              <Text style={[
                styles.viewModeText,
                { color: viewMode === "list" ? colors.primary : colors.textSecondary }
              ]}>
                {language === "nl" ? "Lijst" : "List"}
              </Text>
            </TouchableOpacity>

            {(activeTab === "health" || activeTab === "relapse") && (
              <TouchableOpacity
                style={[
                  styles.viewModeButton,
                  { backgroundColor: colors.card, borderColor: colors.border },
                  viewMode === "calendar" && [
                    styles.activeViewModeButton,
                    { backgroundColor: colors.primary + "15", borderColor: colors.primary + "30" },
                  ],
                ]}
                onPress={() => handleViewModeChange("calendar")}
              >
                <Calendar
                  size={16}
                  color={viewMode === "calendar" ? colors.primary : colors.textSecondary}
                  strokeWidth={2}
                />
                <Text style={[
                  styles.viewModeText,
                  { color: viewMode === "calendar" ? colors.primary : colors.textSecondary }
                ]}>
                  {language === "nl" ? "Kalender" : "Calendar"}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Tab Content */}
        <View style={styles.contentContainer}>
          {renderTabContent()}
        </View>
      </ScrollView>

      {/* Floating Add Button - Only for mood */}
      {activeTab === "mood" && (
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddEntry}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark || colors.primary]}
            style={styles.addButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <PlusCircle size={22} color="#fff" strokeWidth={2.5} />
          </LinearGradient>
        </TouchableOpacity>
      )}

      {/* Modals */}
      {showMoodForm && (
        <MoodForm
          visible={showMoodForm}
          onClose={() => setShowMoodForm(false)}
          colors={colors}
          language={language}
          editingMoodId={editingMoodId}
          profile={profile}
        />
      )}



      {showHealthModal && (
        <LogHealthModal
          visible={showHealthModal}
          onClose={() => setShowHealthModal(false)}
        />
      )}

      {showRelapseForm && (
        <RelapseForm
          visible={showRelapseForm}
          onClose={() => setShowRelapseForm(false)}
          colors={colors}
          language={language}
          profile={profile}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  activeTab: {
    borderWidth: 1,
  },
  activeTabText: {
    fontWeight: "700",
  },
  activeViewModeButton: {
    borderWidth: 1,
  },
  addButton: {
    borderRadius: 28,
    bottom: 20,
    elevation: 8,
    height: 56,
    overflow: "hidden",
    position: "absolute",
    right: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    width: 56,
  },
  addButtonGradient: {
    alignItems: "center",
    height: "100%",
    justifyContent: "center",
    width: "100%",
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100,
    paddingHorizontal: 16,
  },
  headerSection: {
    paddingBottom: 16,
    paddingHorizontal: 20,
    paddingTop: 8,
  },
  mainScrollView: {
    flex: 1,
  },
  pageSubtitle: {
    fontSize: 15,
    lineHeight: 20,
    marginTop: 4,
  },
  pageTitle: {
    fontSize: 28,
    fontWeight: "800",
    letterSpacing: -0.5,
  },
  scrollContent: {
    flexGrow: 1,
  },
  tab: {
    alignItems: "center",
    borderRadius: 12,
    flexDirection: "row",
    justifyContent: "center",
    marginRight: 8,
    minWidth: 100,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 6,
  },
  tabsContainer: {
    flexDirection: "row",
    padding: 4,
  },
  tabsScrollContainer: {
    borderRadius: 16,
    elevation: 2,
    marginBottom: 16,
    marginHorizontal: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
  },
  viewModeButton: {
    alignItems: "center",
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: "row",
    height: 36,
    justifyContent: "center",
    marginHorizontal: 4,
    paddingHorizontal: 8,
    width: "auto",
  },
  viewModeContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  viewModeText: {
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
  },
});